package org.eu.ump90.tradej.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eu.ump90.tradej.bean.domain.Kline;
import org.eu.ump90.tradej.mapper.KlineMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * K线数据控制器
 * 提供K线数据的REST API接口
 */
@RestController
@RequestMapping("/api/kline")
@RequiredArgsConstructor
@Slf4j
public class KlineController {

    private final KlineMapper klineMapper;

    /**
     * 获取所有交易对符号
     * @return 交易对符号列表
     */
    @GetMapping("/symbols")
    public ResponseEntity<List<String>> getAllSymbols() {
        try {
            List<String> symbols = klineMapper.findAllSymbols();
            log.info("获取交易对符号列表成功，共{}个", symbols.size());
            return ResponseEntity.ok(symbols);
        } catch (Exception e) {
            log.error("获取交易对符号列表失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 分页获取K线数据
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 分页K线数据
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getKlineList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 获取数据
            List<Kline> klines = klineMapper.findByPage(offset, size);
            long total = klineMapper.countAll();
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("data", klines);
            response.put("total", total);
            response.put("page", page);
            response.put("size", size);
            response.put("totalPages", (total + size - 1) / size);
            
            log.info("分页获取K线数据成功，页码: {}, 大小: {}, 总数: {}", page, size, total);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("分页获取K线数据失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据交易对符号分页获取K线数据
     * @param symbol 交易对符号
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 分页K线数据
     */
    @GetMapping("/symbol/{symbol}")
    public ResponseEntity<Map<String, Object>> getKlineBySymbol(
            @PathVariable String symbol,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 获取数据
            List<Kline> klines = klineMapper.findBySymbolWithPage(symbol, offset, size);
            long total = klineMapper.countBySymbol(symbol);
            
            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("data", klines);
            response.put("total", total);
            response.put("page", page);
            response.put("size", size);
            response.put("totalPages", (total + size - 1) / size);
            response.put("symbol", symbol);
            
            log.info("根据交易对{}分页获取K线数据成功，页码: {}, 大小: {}, 总数: {}", symbol, page, size, total);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据交易对{}分页获取K线数据失败: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据时间范围获取K线数据（用于图表显示）
     * @param symbol 交易对符号
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return K线数据列表
     */
    @GetMapping("/chart/{symbol}")
    public ResponseEntity<List<Kline>> getKlineForChart(
            @PathVariable String symbol,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime) {
        try {
            List<Kline> klines = klineMapper.findByTimeRange(symbol, startTime, endTime);
            log.info("获取交易对{}的图表数据成功，数据量: {}", symbol, klines.size());
            return ResponseEntity.ok(klines);
        } catch (Exception e) {
            log.error("获取交易对{}的图表数据失败: {}", symbol, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取单个K线数据详情
     * @param id K线数据ID
     * @return K线数据详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Kline> getKlineById(@PathVariable Long id) {
        try {
            Kline kline = klineMapper.selectByPrimaryKey(id);
            if (kline != null) {
                log.info("获取K线数据详情成功，ID: {}", id);
                return ResponseEntity.ok(kline);
            } else {
                log.warn("未找到ID为{}的K线数据", id);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取K线数据详情失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取K线数据统计信息
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getKlineStats() {
        try {
            long totalCount = klineMapper.countAll();
            List<String> symbols = klineMapper.findAllSymbols();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("symbolCount", symbols.size());
            stats.put("symbols", symbols);
            
            log.info("获取K线数据统计信息成功");
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取K线数据统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
