package org.eu.ump90.tradej.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.eu.ump90.tradej.bean.domain.Kline;

@Mapper
public interface KlineMapper {
  int deleteByPrimaryKey(Long id);

  int insert(Kline record);

  int insertSelective(Kline record);

  Kline selectByPrimaryKey(Long id);

  int updateByPrimaryKeySelective(Kline record);

  int updateByPrimaryKey(Kline record);

  int batchInsert(List<Kline> records);

  List<Kline> findBySymbol(String symbol);

  /**
   * 分页查询K线数据
   * @param offset 偏移量
   * @param limit 限制数量
   * @return K线数据列表
   */
  List<Kline> findByPage(@Param("offset") int offset, @Param("limit") int limit);

  /**
   * 根据symbol分页查询K线数据
   * @param symbol 交易对符号
   * @param offset 偏移量
   * @param limit 限制数量
   * @return K线数据列表
   */
  List<Kline> findBySymbolWithPage(@Param("symbol") String symbol, @Param("offset") int offset, @Param("limit") int limit);

  /**
   * 根据时间范围查询K线数据
   * @param symbol 交易对符号
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return K线数据列表
   */
  List<Kline> findByTimeRange(@Param("symbol") String symbol, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

  /**
   * 获取所有不重复的交易对符号
   * @return 交易对符号列表
   */
  List<String> findAllSymbols();

  /**
   * 统计总记录数
   * @return 总记录数
   */
  long countAll();

  /**
   * 根据symbol统计记录数
   * @param symbol 交易对符号
   * @return 记录数
   */
  long countBySymbol(@Param("symbol") String symbol);
}
