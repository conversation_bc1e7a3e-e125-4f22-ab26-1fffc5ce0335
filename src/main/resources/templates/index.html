<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradeJ - 交易数据分析平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link th:href="@{/css/style.css}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                TradeJ
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/kline">
                            <i class="fas fa-chart-candlestick me-1"></i>K线数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sync">
                            <i class="fas fa-sync me-1"></i>数据同步
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="jumbotron bg-primary text-white rounded p-5 mb-4">
            <div class="container">
                <h1 class="display-4">
                    <i class="fas fa-chart-line me-3"></i>
                    欢迎使用 TradeJ
                </h1>
                <p class="lead">专业的加密货币交易数据分析平台，提供实时K线数据查看和分析功能</p>
                <hr class="my-4">
                <p>开始探索数据，发现交易机会</p>
                <a class="btn btn-light btn-lg" href="/kline" role="button">
                    <i class="fas fa-chart-candlestick me-2"></i>
                    查看K线数据
                </a>
            </div>
        </div>

        <!-- 功能卡片 -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-chart-candlestick fa-2x"></i>
                        </div>
                        <h5 class="card-title">K线数据查看</h5>
                        <p class="card-text">查看各种加密货币的K线数据，支持多种时间周期，提供详细的OHLCV数据。</p>
                        <a href="/kline" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>立即查看
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <h5 class="card-title">交互式图表</h5>
                        <p class="card-text">使用专业的图表库展示K线数据，支持缩放、平移等交互操作。</p>
                        <a href="/kline" class="btn btn-success">
                            <i class="fas fa-arrow-right me-1"></i>体验图表
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-sync fa-2x"></i>
                        </div>
                        <h5 class="card-title">数据同步</h5>
                        <p class="card-text">从Binance等交易所同步最新的K线数据，保持数据的实时性和准确性。</p>
                        <a href="/sync" class="btn btn-info">
                            <i class="fas fa-arrow-right me-1"></i>同步数据
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            数据统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-primary" id="totalRecords">-</h3>
                                    <p class="text-muted">总记录数</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-success" id="totalSymbols">-</h3>
                                    <p class="text-muted">交易对数量</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-info">实时</h3>
                                    <p class="text-muted">数据更新</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-warning">24/7</h3>
                                    <p class="text-muted">服务可用</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p>&copy; 2025 TradeJ. 专业的交易数据分析平台.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/common.js}"></script>
    <script>
        // 加载统计数据
        document.addEventListener('DOMContentLoaded', function() {
            fetch('/api/kline/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalRecords').textContent = data.totalCount.toLocaleString();
                    document.getElementById('totalSymbols').textContent = data.symbolCount;
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                    document.getElementById('totalRecords').textContent = '加载失败';
                    document.getElementById('totalSymbols').textContent = '加载失败';
                });
        });
    </script>
</body>
</html>
