<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线数据 - TradeJ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link th:href="@{/css/style.css}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                TradeJ
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/kline">
                            <i class="fas fa-chart-candlestick me-1"></i>K线数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sync">
                            <i class="fas fa-sync me-1"></i>数据同步
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>
                        <i class="fas fa-chart-candlestick me-2"></i>
                        K线数据分析
                    </h2>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="tableViewBtn">
                            <i class="fas fa-table me-1"></i>表格视图
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="chartViewBtn">
                            <i class="fas fa-chart-line me-1"></i>图表视图
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="symbolSelect" class="form-label">交易对</label>
                                <select class="form-select" id="symbolSelect">
                                    <option value="">选择交易对...</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="pageSizeSelect" class="form-label">每页显示</label>
                                <select class="form-select" id="pageSizeSelect">
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="button" class="btn btn-primary" id="searchBtn">
                                        <i class="fas fa-search me-1"></i>查询
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <label class="form-label">快速操作</label>
                                <div>
                                    <button type="button" class="btn btn-success btn-sm me-2" id="refreshBtn">
                                        <i class="fas fa-refresh me-1"></i>刷新
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" id="showChartBtn">
                                        <i class="fas fa-chart-line me-1"></i>显示图表
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表格视图 -->
        <div id="tableView">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                K线数据列表
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>交易对</th>
                                            <th>开盘时间</th>
                                            <th>开盘价</th>
                                            <th>最高价</th>
                                            <th>最低价</th>
                                            <th>收盘价</th>
                                            <th>成交量</th>
                                            <th>交易次数</th>
                                            <th>时间周期</th>
                                        </tr>
                                    </thead>
                                    <tbody id="klineTableBody">
                                        <!-- 数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页控件 -->
                            <nav aria-label="分页导航">
                                <ul class="pagination justify-content-center" id="pagination">
                                    <!-- 分页按钮将通过JavaScript动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表视图 -->
        <div id="chartView" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                K线图表
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="klineChart" style="height: 600px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">数据加载中，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script th:src="@{/js/common.js}"></script>
    <script th:src="@{/js/kline.js}"></script>
</body>
</html>
