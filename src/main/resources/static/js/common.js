/**
 * TradeJ 通用JavaScript工具函数
 */

// 全局配置
const TradeJ = {
    // API基础路径
    API_BASE: '/api',
    
    // 默认配置
    DEFAULT_PAGE_SIZE: 20,
    
    // 时间格式化选项
    TIME_FORMAT_OPTIONS: {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }
};

/**
 * 工具函数类
 */
class Utils {
    /**
     * 格式化时间戳为可读字符串
     * @param {number} timestamp 时间戳（毫秒）
     * @returns {string} 格式化后的时间字符串
     */
    static formatTimestamp(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', TradeJ.TIME_FORMAT_OPTIONS);
    }

    /**
     * 格式化数字，添加千分位分隔符
     * @param {number} num 数字
     * @param {number} decimals 小数位数
     * @returns {string} 格式化后的数字字符串
     */
    static formatNumber(num, decimals = 2) {
        if (num === null || num === undefined || isNaN(num)) return '-';
        return Number(num).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    /**
     * 格式化价格，根据价格大小自动调整小数位数
     * @param {number} price 价格
     * @returns {string} 格式化后的价格字符串
     */
    static formatPrice(price) {
        if (price === null || price === undefined || isNaN(price)) return '-';
        
        const num = Number(price);
        if (num >= 1000) {
            return this.formatNumber(num, 2);
        } else if (num >= 1) {
            return this.formatNumber(num, 4);
        } else {
            return this.formatNumber(num, 8);
        }
    }

    /**
     * 格式化成交量
     * @param {number} volume 成交量
     * @returns {string} 格式化后的成交量字符串
     */
    static formatVolume(volume) {
        if (volume === null || volume === undefined || isNaN(volume)) return '-';
        
        const num = Number(volume);
        if (num >= 1000000000) {
            return (num / 1000000000).toFixed(2) + 'B';
        } else if (num >= 1000000) {
            return (num / 1000000).toFixed(2) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(2) + 'K';
        } else {
            return this.formatNumber(num, 2);
        }
    }

    /**
     * 计算价格变化的CSS类名
     * @param {number} open 开盘价
     * @param {number} close 收盘价
     * @returns {string} CSS类名
     */
    static getPriceChangeClass(open, close) {
        if (!open || !close) return 'price-neutral';
        
        if (close > open) {
            return 'price-up';
        } else if (close < open) {
            return 'price-down';
        } else {
            return 'price-neutral';
        }
    }

    /**
     * 显示加载提示
     */
    static showLoading() {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            const modal = new bootstrap.Modal(loadingModal);
            modal.show();
        }
    }

    /**
     * 隐藏加载提示
     */
    static hideLoading() {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            const modal = bootstrap.Modal.getInstance(loadingModal);
            if (modal) {
                modal.hide();
            }
        }
    }

    /**
     * 显示错误提示
     * @param {string} message 错误消息
     */
    static showError(message) {
        // 创建错误提示元素
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    /**
     * 显示成功提示
     * @param {string} message 成功消息
     */
    static showSuccess(message) {
        // 创建成功提示元素
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

/**
 * API请求类
 */
class ApiClient {
    /**
     * 发送GET请求
     * @param {string} url 请求URL
     * @returns {Promise} 请求Promise
     */
    static async get(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('API GET request failed:', error);
            throw error;
        }
    }

    /**
     * 发送POST请求
     * @param {string} url 请求URL
     * @param {Object} data 请求数据
     * @returns {Promise} 请求Promise
     */
    static async post(url, data) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('API POST request failed:', error);
            throw error;
        }
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 为所有表格添加响应式类
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
});

// 导出到全局作用域
window.TradeJ = TradeJ;
window.Utils = Utils;
window.ApiClient = ApiClient;
