/**
 * K线数据页面JavaScript
 */

class KlineManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentSymbol = '';
        this.chart = null;
        this.currentView = 'table'; // 'table' 或 'chart'
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadSymbols();
        this.loadKlineData();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 视图切换按钮
        document.getElementById('tableViewBtn').addEventListener('click', () => {
            this.switchView('table');
        });
        
        document.getElementById('chartViewBtn').addEventListener('click', () => {
            this.switchView('chart');
        });

        // 搜索按钮
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.search();
        });

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refresh();
        });

        // 显示图表按钮
        document.getElementById('showChartBtn').addEventListener('click', () => {
            this.showChart();
        });

        // 交易对选择变化
        document.getElementById('symbolSelect').addEventListener('change', (e) => {
            this.currentSymbol = e.target.value;
        });

        // 每页大小选择变化
        document.getElementById('pageSizeSelect').addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.loadKlineData();
        });
    }

    /**
     * 切换视图
     * @param {string} view 视图类型：'table' 或 'chart'
     */
    switchView(view) {
        this.currentView = view;
        
        // 更新按钮状态
        document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');
        document.getElementById('chartViewBtn').classList.toggle('active', view === 'chart');
        
        // 显示/隐藏对应视图
        document.getElementById('tableView').style.display = view === 'table' ? 'block' : 'none';
        document.getElementById('chartView').style.display = view === 'chart' ? 'block' : 'none';
        
        if (view === 'chart' && !this.chart) {
            this.initChart();
        }
    }

    /**
     * 加载交易对列表
     */
    async loadSymbols() {
        try {
            const symbols = await ApiClient.get(`${TradeJ.API_BASE}/kline/symbols`);
            const select = document.getElementById('symbolSelect');
            
            // 清空现有选项（保留第一个默认选项）
            select.innerHTML = '<option value="">选择交易对...</option>';
            
            // 添加交易对选项
            symbols.forEach(symbol => {
                const option = document.createElement('option');
                option.value = symbol;
                option.textContent = symbol;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('加载交易对列表失败:', error);
            Utils.showError('加载交易对列表失败');
        }
    }

    /**
     * 加载K线数据
     */
    async loadKlineData() {
        try {
            Utils.showLoading();
            
            let url = `${TradeJ.API_BASE}/kline/list?page=${this.currentPage}&size=${this.pageSize}`;
            if (this.currentSymbol) {
                url = `${TradeJ.API_BASE}/kline/symbol/${this.currentSymbol}?page=${this.currentPage}&size=${this.pageSize}`;
            }
            
            const response = await ApiClient.get(url);
            this.renderTable(response);
            this.renderPagination(response);
            
        } catch (error) {
            console.error('加载K线数据失败:', error);
            Utils.showError('加载K线数据失败');
        } finally {
            Utils.hideLoading();
        }
    }

    /**
     * 渲染表格
     * @param {Object} response API响应数据
     */
    renderTable(response) {
        const tbody = document.getElementById('klineTableBody');
        tbody.innerHTML = '';
        
        if (!response.data || response.data.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        暂无数据
                    </td>
                </tr>
            `;
            return;
        }
        
        response.data.forEach(kline => {
            const row = document.createElement('tr');
            const priceClass = Utils.getPriceChangeClass(kline.open, kline.close);
            
            row.innerHTML = `
                <td>${kline.id}</td>
                <td><span class="badge bg-primary">${kline.symbol}</span></td>
                <td>${Utils.formatTimestamp(kline.openTime)}</td>
                <td class="${priceClass}">${Utils.formatPrice(kline.open)}</td>
                <td class="price-up">${Utils.formatPrice(kline.high)}</td>
                <td class="price-down">${Utils.formatPrice(kline.low)}</td>
                <td class="${priceClass}">${Utils.formatPrice(kline.close)}</td>
                <td>${Utils.formatVolume(kline.volume)}</td>
                <td>${Utils.formatNumber(kline.numberOfTrades, 0)}</td>
                <td><span class="badge bg-secondary">${kline.interval || '-'}</span></td>
            `;
            
            tbody.appendChild(row);
        });
    }

    /**
     * 渲染分页控件
     * @param {Object} response API响应数据
     */
    renderPagination(response) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';
        
        const totalPages = response.totalPages || 1;
        const currentPage = response.page || 1;
        
        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <a class="page-link" href="#" data-page="${currentPage - 1}">
                <i class="fas fa-chevron-left"></i>
            </a>
        `;
        pagination.appendChild(prevLi);
        
        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(li);
        }
        
        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <a class="page-link" href="#" data-page="${currentPage + 1}">
                <i class="fas fa-chevron-right"></i>
            </a>
        `;
        pagination.appendChild(nextLi);
        
        // 绑定分页点击事件
        pagination.addEventListener('click', (e) => {
            e.preventDefault();
            if (e.target.closest('.page-link') && !e.target.closest('.disabled')) {
                const page = parseInt(e.target.closest('.page-link').dataset.page);
                if (page && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadKlineData();
                }
            }
        });
    }

    /**
     * 搜索
     */
    search() {
        this.currentPage = 1;
        this.loadKlineData();
    }

    /**
     * 刷新
     */
    refresh() {
        this.loadKlineData();
        if (this.currentView === 'chart') {
            this.loadChartData();
        }
        Utils.showSuccess('数据已刷新');
    }

    /**
     * 显示图表
     */
    showChart() {
        if (!this.currentSymbol) {
            Utils.showError('请先选择交易对');
            return;
        }
        
        this.switchView('chart');
        this.loadChartData();
    }

    /**
     * 初始化图表
     */
    initChart() {
        const chartDom = document.getElementById('klineChart');
        this.chart = echarts.init(chartDom);
        
        // 设置图表配置
        const option = {
            title: {
                text: 'K线图',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['K线', '成交量'],
                top: 30
            },
            grid: [
                {
                    left: '10%',
                    right: '8%',
                    height: '50%'
                },
                {
                    left: '10%',
                    right: '8%',
                    top: '70%',
                    height: '16%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    data: [],
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    splitLine: { show: false },
                    min: 'dataMin',
                    max: 'dataMax'
                },
                {
                    type: 'category',
                    gridIndex: 1,
                    data: [],
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false },
                    min: 'dataMin',
                    max: 'dataMax'
                }
            ],
            yAxis: [
                {
                    scale: true,
                    splitArea: {
                        show: true
                    }
                },
                {
                    scale: true,
                    gridIndex: 1,
                    splitNumber: 2,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false }
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 80,
                    end: 100
                },
                {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    top: '85%',
                    start: 80,
                    end: 100
                }
            ],
            series: [
                {
                    name: 'K线',
                    type: 'candlestick',
                    data: [],
                    itemStyle: {
                        color: '#ef232a',
                        color0: '#14b143',
                        borderColor: '#ef232a',
                        borderColor0: '#14b143'
                    }
                },
                {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: []
                }
            ]
        };
        
        this.chart.setOption(option);
    }

    /**
     * 加载图表数据
     */
    async loadChartData() {
        if (!this.currentSymbol) {
            return;
        }
        
        try {
            Utils.showLoading();
            
            const data = await ApiClient.get(`${TradeJ.API_BASE}/kline/chart/${this.currentSymbol}`);
            this.renderChart(data);
            
        } catch (error) {
            console.error('加载图表数据失败:', error);
            Utils.showError('加载图表数据失败');
        } finally {
            Utils.hideLoading();
        }
    }

    /**
     * 渲染图表
     * @param {Array} data K线数据
     */
    renderChart(data) {
        if (!this.chart || !data || data.length === 0) {
            return;
        }
        
        const dates = [];
        const klineData = [];
        const volumeData = [];
        
        data.forEach(item => {
            const date = Utils.formatTimestamp(item.openTime);
            dates.push(date);
            klineData.push([item.open, item.close, item.low, item.high]);
            volumeData.push(item.volume);
        });
        
        this.chart.setOption({
            title: {
                text: `${this.currentSymbol} K线图`
            },
            xAxis: [
                { data: dates },
                { data: dates }
            ],
            series: [
                { data: klineData },
                { data: volumeData }
            ]
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.klineManager = new KlineManager();
});
