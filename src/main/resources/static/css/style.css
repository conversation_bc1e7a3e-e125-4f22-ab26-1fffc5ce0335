/* TradeJ 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 功能图标样式 */
.feature-icon {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
}

/* 统计项样式 */
.stat-item h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-item p {
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* 欢迎区域样式 */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 表格样式 */
.table {
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.1);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* 表单控件样式 */
.form-select, .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 图表容器样式 */
#klineChart {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.5rem;
    }
    
    .stat-item h3 {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 价格颜色 */
.price-up {
    color: #28a745;
    font-weight: bold;
}

.price-down {
    color: #dc3545;
    font-weight: bold;
}

.price-neutral {
    color: #6c757d;
}

/* 数据表格特殊样式 */
.data-table .table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.data-table .table th {
    background-color: #343a40;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* 图表工具栏样式 */
.chart-toolbar {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: #28a745;
}

.status-offline {
    background-color: #dc3545;
}

.status-warning {
    background-color: #ffc107;
}
