<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.eu.ump90.tradej.mapper.KlineMapper">
  <resultMap id="BaseResultMap" type="org.eu.ump90.tradej.bean.domain.Kline">
    <!--@mbg.generated-->
    <!--@Table kline-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="open_time" jdbcType="BIGINT" property="openTime" />
    <result column="open" jdbcType="DECIMAL" property="open" />
    <result column="high" jdbcType="DECIMAL" property="high" />
    <result column="low" jdbcType="DECIMAL" property="low" />
    <result column="close" jdbcType="DECIMAL" property="close" />
    <result column="volume" jdbcType="DECIMAL" property="volume" />
    <result column="close_time" jdbcType="BIGINT" property="closeTime" />
    <result column="quote_asset_volume" jdbcType="DECIMAL" property="quoteAssetVolume" />
    <result column="number_of_trades" jdbcType="INTEGER" property="numberOfTrades" />
    <result column="taker_buy_base_asset_volume" jdbcType="DECIMAL" property="takerBuyBaseAssetVolume" />
    <result column="taker_buy_quote_asset_volume" jdbcType="DECIMAL" property="takerBuyQuoteAssetVolume" />
    <result column="ignore_field" jdbcType="VARCHAR" property="ignoreField" />
      <result column="interval" jdbcType="VARCHAR" property="interval" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, symbol, open_time, `open`, high, low, `close`, volume, close_time, quote_asset_volume, 
    number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, ignore_field, `interval`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from kline
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from kline
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="org.eu.ump90.tradej.bean.domain.Kline" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into kline (symbol, open_time, `open`,
      high, low, `close`, volume, 
      close_time, quote_asset_volume, number_of_trades, 
      taker_buy_base_asset_volume, taker_buy_quote_asset_volume, 
      ignore_field, `interval`)
    values (#{symbol,jdbcType=VARCHAR}, #{openTime,jdbcType=BIGINT}, #{open,jdbcType=DECIMAL}, 
      #{high,jdbcType=DECIMAL}, #{low,jdbcType=DECIMAL}, #{close,jdbcType=DECIMAL}, #{volume,jdbcType=DECIMAL}, 
      #{closeTime,jdbcType=BIGINT}, #{quoteAssetVolume,jdbcType=DECIMAL}, #{numberOfTrades,jdbcType=INTEGER}, 
      #{takerBuyBaseAssetVolume,jdbcType=DECIMAL}, #{takerBuyQuoteAssetVolume,jdbcType=DECIMAL}, 
      #{ignoreField,jdbcType=VARCHAR}, #{interval,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="org.eu.ump90.tradej.bean.domain.Kline" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into kline
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="symbol != null">
        symbol,
      </if>
      <if test="openTime != null">
        open_time,
      </if>
      <if test="open != null">
        `open`,
      </if>
      <if test="high != null">
        high,
      </if>
      <if test="low != null">
        low,
      </if>
      <if test="close != null">
        `close`,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="quoteAssetVolume != null">
        quote_asset_volume,
      </if>
      <if test="numberOfTrades != null">
        number_of_trades,
      </if>
      <if test="takerBuyBaseAssetVolume != null">
        taker_buy_base_asset_volume,
      </if>
      <if test="takerBuyQuoteAssetVolume != null">
        taker_buy_quote_asset_volume,
      </if>
      <if test="ignoreField != null">
        ignore_field,
      </if>
        <if test="interval != null">
        `interval`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="symbol != null">
        #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="openTime != null">
        #{openTime,jdbcType=BIGINT},
      </if>
      <if test="open != null">
        #{open,jdbcType=DECIMAL},
      </if>
      <if test="high != null">
        #{high,jdbcType=DECIMAL},
      </if>
      <if test="low != null">
        #{low,jdbcType=DECIMAL},
      </if>
      <if test="close != null">
        #{close,jdbcType=DECIMAL},
      </if>
      <if test="volume != null">
        #{volume,jdbcType=DECIMAL},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=BIGINT},
      </if>
      <if test="quoteAssetVolume != null">
        #{quoteAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="numberOfTrades != null">
        #{numberOfTrades,jdbcType=INTEGER},
      </if>
      <if test="takerBuyBaseAssetVolume != null">
        #{takerBuyBaseAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="takerBuyQuoteAssetVolume != null">
        #{takerBuyQuoteAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="ignoreField != null">
        #{ignoreField,jdbcType=VARCHAR},
      </if>
        <if test="interval != null">
        #{interval,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.eu.ump90.tradej.bean.domain.Kline">
    <!--@mbg.generated-->
    update kline
    <set>
      <if test="symbol != null">
        symbol = #{symbol,jdbcType=VARCHAR},
      </if>
      <if test="openTime != null">
        open_time = #{openTime,jdbcType=BIGINT},
      </if>
      <if test="open != null">
        `open` = #{open,jdbcType=DECIMAL},
      </if>
      <if test="high != null">
        high = #{high,jdbcType=DECIMAL},
      </if>
      <if test="low != null">
        low = #{low,jdbcType=DECIMAL},
      </if>
      <if test="close != null">
        `close` = #{close,jdbcType=DECIMAL},
      </if>
      <if test="volume != null">
        volume = #{volume,jdbcType=DECIMAL},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=BIGINT},
      </if>
      <if test="quoteAssetVolume != null">
        quote_asset_volume = #{quoteAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="numberOfTrades != null">
        number_of_trades = #{numberOfTrades,jdbcType=INTEGER},
      </if>
      <if test="takerBuyBaseAssetVolume != null">
        taker_buy_base_asset_volume = #{takerBuyBaseAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="takerBuyQuoteAssetVolume != null">
        taker_buy_quote_asset_volume = #{takerBuyQuoteAssetVolume,jdbcType=DECIMAL},
      </if>
      <if test="ignoreField != null">
        ignore_field = #{ignoreField,jdbcType=VARCHAR},
      </if>
        <if test="interval != null">
        `interval` = #{interval,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.eu.ump90.tradej.bean.domain.Kline">
    <!--@mbg.generated-->
    update kline
    set symbol = #{symbol,jdbcType=VARCHAR},
      open_time = #{openTime,jdbcType=BIGINT},
      `open` = #{open,jdbcType=DECIMAL},
      high = #{high,jdbcType=DECIMAL},
      low = #{low,jdbcType=DECIMAL},
      `close` = #{close,jdbcType=DECIMAL},
      volume = #{volume,jdbcType=DECIMAL},
      close_time = #{closeTime,jdbcType=BIGINT},
      quote_asset_volume = #{quoteAssetVolume,jdbcType=DECIMAL},
      number_of_trades = #{numberOfTrades,jdbcType=INTEGER},
      taker_buy_base_asset_volume = #{takerBuyBaseAssetVolume,jdbcType=DECIMAL},
      taker_buy_quote_asset_volume = #{takerBuyQuoteAssetVolume,jdbcType=DECIMAL},
      ignore_field = #{ignoreField,jdbcType=VARCHAR},
      `interval` = #{interval,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
      insert into kline (symbol, open_time, `open`, high, low, `close`, volume, close_time, quote_asset_volume,
                            number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, ignore_field,
                            `interval`)
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.symbol,jdbcType=VARCHAR},
           #{item.openTime,jdbcType=BIGINT},
           #{item.open,jdbcType=DECIMAL},
           #{item.high,jdbcType=DECIMAL},
           #{item.low,jdbcType=DECIMAL},
           #{item.close,jdbcType=DECIMAL},
           #{item.volume,jdbcType=DECIMAL},
           #{item.closeTime,jdbcType=BIGINT},
           #{item.quoteAssetVolume,jdbcType=DECIMAL},
           #{item.numberOfTrades,jdbcType=INTEGER},
           #{item.takerBuyBaseAssetVolume,jdbcType=DECIMAL},
           #{item.takerBuyQuoteAssetVolume,jdbcType=DECIMAL},
           #{item.ignoreField,jdbcType=VARCHAR},
           #{item.interval,jdbcType=VARCHAR})
      </foreach>
  </insert>
  <select id="findBySymbol" parameterType="java.lang.String" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from kline
      where symbol = #{symbol,jdbcType=VARCHAR}
      order by open_time desc
  </select>

  <!-- 分页查询K线数据 -->
  <select id="findByPage" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from kline
      order by open_time desc
      limit #{offset}, #{limit}
  </select>

  <!-- 根据symbol分页查询K线数据 -->
  <select id="findBySymbolWithPage" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from kline
      where symbol = #{symbol,jdbcType=VARCHAR}
      order by open_time desc
      limit #{offset}, #{limit}
  </select>

  <!-- 根据时间范围查询K线数据 -->
  <select id="findByTimeRange" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from kline
      where symbol = #{symbol,jdbcType=VARCHAR}
      <if test="startTime != null">
          and open_time >= #{startTime,jdbcType=BIGINT}
      </if>
      <if test="endTime != null">
          and open_time &lt;= #{endTime,jdbcType=BIGINT}
      </if>
      order by open_time asc
  </select>

  <!-- 获取所有不重复的交易对符号 -->
  <select id="findAllSymbols" resultType="java.lang.String">
      select distinct symbol
      from kline
      order by symbol
  </select>

  <!-- 统计总记录数 -->
  <select id="countAll" resultType="java.lang.Long">
      select count(*)
      from kline
  </select>

  <!-- 根据symbol统计记录数 -->
  <select id="countBySymbol" resultType="java.lang.Long">
      select count(*)
      from kline
      where symbol = #{symbol,jdbcType=VARCHAR}
  </select>
</mapper>